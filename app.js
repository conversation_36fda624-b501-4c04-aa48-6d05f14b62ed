class XiaohongshuContentApp {
    constructor() {
        this.contentGenerator = new ContentGenerator();
        this.imageGenerator = null;
        this.currentContent = null;
        this.currentStyle = 'gradient';
        this.currentType = '副业';
        this.history = this.loadHistory();
        
        this.init();
    }

    init() {
        // 初始化图片生成器
        const canvas = document.getElementById('imageCanvas');
        this.imageGenerator = new ImageGenerator(canvas);
        
        // 绑定事件监听器
        this.bindEvents();
        
        // 加载历史记录
        this.renderHistory();
        
        // 生成初始内容
        this.generateContent();
    }

    bindEvents() {
        // 内容类型选择
        document.querySelectorAll('.type-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.type-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentType = e.target.dataset.type;
            });
        });

        // 图片风格选择
        document.querySelectorAll('.style-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                document.querySelectorAll('.style-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
                this.currentStyle = e.target.dataset.style;
                
                // 如果已有内容，重新生成图片
                if (this.currentContent) {
                    this.generateImage();
                }
            });
        });

        // 生成按钮
        document.getElementById('generateBtn').addEventListener('click', () => {
            this.generateContent();
        });

        // 重新生成图片按钮
        document.getElementById('regenerateImage').addEventListener('click', () => {
            if (this.currentContent) {
                this.generateImage();
            }
        });

        // 下载图片按钮
        document.getElementById('downloadImage').addEventListener('click', () => {
            if (this.currentContent) {
                this.downloadImage();
            }
        });

        // 设置变更监听
        document.getElementById('titleLength').addEventListener('change', () => {
            if (this.currentContent) {
                this.generateContent();
            }
        });

        document.getElementById('contentDetail').addEventListener('change', () => {
            if (this.currentContent) {
                this.generateContent();
            }
        });
    }

    async generateContent() {
        try {
            // 显示加载状态
            this.showLoading();
            
            // 获取设置
            const titleLength = document.getElementById('titleLength').value;
            const contentDetail = document.getElementById('contentDetail').value;
            
            // 生成内容
            this.currentContent = this.contentGenerator.generateContent(
                this.currentType,
                titleLength,
                contentDetail
            );
            
            if (!this.currentContent) {
                throw new Error('内容生成失败');
            }
            
            // 更新预览
            this.updateContentPreview();
            
            // 生成图片
            await this.generateImage();
            
            // 保存到历史记录
            this.saveToHistory(this.currentContent);
            
            // 隐藏加载状态
            this.hideLoading();
            
        } catch (error) {
            console.error('生成内容时出错:', error);
            this.showError('生成内容失败，请重试');
            this.hideLoading();
        }
    }

    async generateImage() {
        if (!this.currentContent) return;
        
        try {
            await this.imageGenerator.generateImage(this.currentContent, this.currentStyle);
        } catch (error) {
            console.error('生成图片时出错:', error);
            this.showError('生成图片失败，请重试');
        }
    }

    updateContentPreview() {
        if (!this.currentContent) return;
        
        document.getElementById('titlePreview').textContent = this.currentContent.title;
        document.getElementById('contentText').textContent = this.currentContent.content;
        document.getElementById('hashtags').textContent = this.currentContent.hashtags;
    }

    downloadImage() {
        if (!this.imageGenerator) return;
        
        const filename = `xiaohongshu-${this.currentType}-${Date.now()}`;
        this.imageGenerator.downloadImage(filename);
        
        // 显示下载成功提示
        this.showSuccess('图片下载成功！');
    }

    saveToHistory(content) {
        // 添加到历史记录开头
        this.history.unshift({
            ...content,
            id: Date.now(),
            style: this.currentStyle
        });
        
        // 限制历史记录数量
        if (this.history.length > 20) {
            this.history = this.history.slice(0, 20);
        }
        
        // 保存到本地存储
        localStorage.setItem('xiaohongshu_history', JSON.stringify(this.history));
        
        // 更新历史记录显示
        this.renderHistory();
    }

    loadHistory() {
        try {
            const saved = localStorage.getItem('xiaohongshu_history');
            return saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('加载历史记录失败:', error);
            return [];
        }
    }

    renderHistory() {
        const historyList = document.getElementById('historyList');
        
        if (this.history.length === 0) {
            historyList.innerHTML = '<div class="history-item"><span>暂无历史记录</span></div>';
            return;
        }
        
        historyList.innerHTML = this.history.map(item => `
            <div class="history-item" data-id="${item.id}">
                <div class="history-title">${item.title}</div>
                <div class="history-meta">
                    <span class="history-type">${item.type}</span>
                    <span class="history-style">${item.style}</span>
                    <span class="history-time">${this.formatTime(item.timestamp)}</span>
                </div>
            </div>
        `).join('');
        
        // 绑定历史记录点击事件
        historyList.querySelectorAll('.history-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const id = parseInt(e.currentTarget.dataset.id);
                this.loadFromHistory(id);
            });
        });
    }

    loadFromHistory(id) {
        const item = this.history.find(h => h.id === id);
        if (!item) return;
        
        // 设置当前内容
        this.currentContent = item;
        this.currentType = item.type;
        this.currentStyle = item.style;
        
        // 更新UI状态
        document.querySelectorAll('.type-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.type === item.type);
        });
        
        document.querySelectorAll('.style-btn').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.style === item.style);
        });
        
        // 更新预览
        this.updateContentPreview();
        this.generateImage();
        
        this.showSuccess('已加载历史记录');
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
        return `${Math.floor(diff / 86400000)}天前`;
    }

    showLoading() {
        const btn = document.getElementById('generateBtn');
        btn.textContent = '🔄 生成中...';
        btn.disabled = true;
    }

    hideLoading() {
        const btn = document.getElementById('generateBtn');
        btn.textContent = '🎯 一键生成内容';
        btn.disabled = false;
    }

    showSuccess(message) {
        this.showMessage(message, 'success');
    }

    showError(message) {
        this.showMessage(message, 'error');
    }

    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `message message-${type}`;
        messageEl.textContent = message;
        
        // 添加样式
        Object.assign(messageEl.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: '500',
            zIndex: '1000',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });
        
        if (type === 'success') {
            messageEl.style.background = 'linear-gradient(45deg, #2ecc71, #27ae60)';
        } else if (type === 'error') {
            messageEl.style.background = 'linear-gradient(45deg, #e74c3c, #c0392b)';
        } else {
            messageEl.style.background = 'linear-gradient(45deg, #3498db, #2980b9)';
        }
        
        document.body.appendChild(messageEl);
        
        // 显示动画
        setTimeout(() => {
            messageEl.style.transform = 'translateX(0)';
        }, 100);
        
        // 自动隐藏
        setTimeout(() => {
            messageEl.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(messageEl);
            }, 300);
        }, 3000);
    }

    // 导出功能
    exportContent() {
        if (!this.currentContent) return;
        
        const exportData = {
            title: this.currentContent.title,
            content: this.currentContent.content,
            hashtags: this.currentContent.hashtags,
            type: this.currentContent.type,
            timestamp: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });
        
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `xiaohongshu-content-${Date.now()}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
        this.showSuccess('内容导出成功！');
    }

    // 批量生成功能
    async batchGenerate(count = 5) {
        const results = [];
        
        for (let i = 0; i < count; i++) {
            const content = this.contentGenerator.generateContent(this.currentType);
            if (content) {
                results.push(content);
                this.saveToHistory(content);
            }
        }
        
        this.showSuccess(`批量生成了 ${results.length} 条内容`);
        return results;
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    window.app = new XiaohongshuContentApp();
});

// 添加键盘快捷键
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
            case 'Enter':
                e.preventDefault();
                if (window.app) window.app.generateContent();
                break;
            case 's':
                e.preventDefault();
                if (window.app) window.app.downloadImage();
                break;
            case 'e':
                e.preventDefault();
                if (window.app) window.app.exportContent();
                break;
        }
    }
});
