* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header p {
    font-size: 1.1rem;
    color: #666;
    font-weight: 400;
}

.main-content {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.control-panel {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.section {
    margin-bottom: 25px;
}

.section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.content-types, .style-types {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.type-btn, .style-btn {
    padding: 12px 16px;
    border: 2px solid #e1e5e9;
    background: white;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    text-align: left;
}

.type-btn:hover, .style-btn:hover {
    border-color: #4ecdc4;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(78, 205, 196, 0.2);
}

.type-btn.active, .style-btn.active {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
    border-color: transparent;
}

.settings {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.settings label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
}

.settings select {
    padding: 8px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    background: white;
    font-size: 0.9rem;
    min-width: 140px;
}

.generate-btn {
    width: 100%;
    padding: 16px;
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.generate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
}

.preview-area {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 20px;
}

.preview-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.preview-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.content-preview {
    min-height: 300px;
}

.title-preview {
    font-size: 1.3rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 15px;
    line-height: 1.4;
}

.content-text {
    font-size: 1rem;
    line-height: 1.6;
    color: #555;
    margin-bottom: 20px;
    white-space: pre-line;
}

.hashtags {
    color: #4ecdc4;
    font-weight: 500;
    font-size: 0.95rem;
}

.image-preview {
    text-align: center;
}

#imageCanvas {
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    max-width: 100%;
    height: auto;
    margin-bottom: 15px;
}

.image-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.action-btn {
    padding: 10px 16px;
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(78, 205, 196, 0.3);
}

.history-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 25px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.history-section h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
}

.history-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
}

.history-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
    border-left: 4px solid #4ecdc4;
    cursor: pointer;
    transition: all 0.3s ease;
}

.history-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.history-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 0.95rem;
    line-height: 1.3;
}

.history-meta {
    display: flex;
    gap: 10px;
    font-size: 0.8rem;
    color: #666;
}

.history-type {
    background: linear-gradient(45deg, #4ecdc4, #44a08d);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
}

.history-style {
    background: #f8f9fa;
    color: #666;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
}

.history-time {
    color: #999;
    font-size: 0.75rem;
}

/* 消息提示样式 */
.message {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

/* 加载状态 */
.generate-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
    }

    .preview-area {
        grid-template-columns: 1fr;
    }

    #imageCanvas {
        max-width: 300px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .content-types, .style-types {
        grid-template-columns: 1fr;
    }

    .main-content {
        gap: 20px;
    }

    .preview-area {
        gap: 15px;
    }

    .control-panel, .preview-section, .history-section {
        padding: 20px;
    }

    #imageCanvas {
        max-width: 250px;
    }

    .image-actions {
        flex-direction: column;
        gap: 8px;
    }

    .action-btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 20px;
    }

    .header h1 {
        font-size: 1.8rem;
    }

    .header p {
        font-size: 1rem;
    }

    .control-panel, .preview-section, .history-section {
        padding: 15px;
    }

    .settings {
        gap: 12px;
    }

    .settings label {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .settings select {
        width: 100%;
        min-width: auto;
    }

    #imageCanvas {
        max-width: 200px;
    }
}
