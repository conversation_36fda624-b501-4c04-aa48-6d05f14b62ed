<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书赚钱内容生成器</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🔥 小红书赚钱内容生成器</h1>
            <p>一键生成爆款赚钱内容 + 精美配图</p>
        </header>

        <div class="main-content">
            <!-- 控制面板 -->
            <div class="control-panel">
                <div class="section">
                    <h3>📝 内容类型</h3>
                    <div class="content-types">
                        <button class="type-btn active" data-type="副业">💼 副业推荐</button>
                        <button class="type-btn" data-type="理财">💰 理财技巧</button>
                        <button class="type-btn" data-type="技能">🎯 技能变现</button>
                        <button class="type-btn" data-type="创业">🚀 创业分享</button>
                        <button class="type-btn" data-type="省钱">💡 省钱攻略</button>
                    </div>
                </div>

                <div class="section">
                    <h3>🎨 图片风格</h3>
                    <div class="style-types">
                        <button class="style-btn active" data-style="gradient">🌈 渐变风</button>
                        <button class="style-btn" data-style="minimal">✨ 简约风</button>
                        <button class="style-btn" data-style="vibrant">🔥 活力风</button>
                        <button class="style-btn" data-style="professional">💼 商务风</button>
                    </div>
                </div>

                <div class="section">
                    <h3>⚙️ 生成设置</h3>
                    <div class="settings">
                        <label>
                            <span>标题长度:</span>
                            <select id="titleLength">
                                <option value="short">简短 (10-15字)</option>
                                <option value="medium" selected>中等 (15-20字)</option>
                                <option value="long">较长 (20-25字)</option>
                            </select>
                        </label>
                        <label>
                            <span>内容详细度:</span>
                            <select id="contentDetail">
                                <option value="brief">简要</option>
                                <option value="detailed" selected>详细</option>
                                <option value="comprehensive">全面</option>
                            </select>
                        </label>
                    </div>
                </div>

                <button class="generate-btn" id="generateBtn">
                    🎯 一键生成内容
                </button>

                <div class="batch-actions">
                    <button class="batch-btn" id="batchGenerateBtn">
                        📦 批量生成(5条)
                    </button>
                    <button class="batch-btn" id="exportBtn">
                        📤 导出内容
                    </button>
                </div>
            </div>

            <!-- 预览区域 -->
            <div class="preview-area">
                <div class="preview-section">
                    <h3>📱 内容预览</h3>
                    <div class="content-preview">
                        <div class="title-preview" id="titlePreview">
                            点击生成按钮开始创建内容...
                        </div>
                        <div class="content-text" id="contentText">
                            这里将显示生成的小红书文案内容
                        </div>
                        <div class="hashtags" id="hashtags">
                            #赚钱 #副业 #理财
                        </div>
                    </div>
                </div>

                <div class="preview-section">
                    <h3>🖼️ 图片预览</h3>
                    <div class="image-preview">
                        <canvas id="imageCanvas" width="400" height="600"></canvas>
                        <div class="image-actions">
                            <button class="action-btn" id="regenerateImage">🔄 重新生成图片</button>
                            <button class="action-btn" id="downloadImage">📥 下载图片</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 历史记录 -->
        <div class="history-section">
            <h3>📚 生成历史</h3>
            <div class="history-list" id="historyList">
                <div class="history-item">
                    <span>暂无历史记录</span>
                </div>
            </div>
        </div>
    </div>

    <script src="contentGenerator.js"></script>
    <script src="imageGenerator.js"></script>
    <script src="app.js"></script>
</body>
</html>
