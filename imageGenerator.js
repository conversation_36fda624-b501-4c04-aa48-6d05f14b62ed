class ImageGenerator {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.width = 400;
        this.height = 600;
        
        // 设置画布尺寸
        this.canvas.width = this.width;
        this.canvas.height = this.height;
        
        this.styles = {
            gradient: {
                backgrounds: [
                    ['#667eea', '#764ba2'],
                    ['#f093fb', '#f5576c'],
                    ['#4facfe', '#00f2fe'],
                    ['#43e97b', '#38f9d7'],
                    ['#fa709a', '#fee140'],
                    ['#a8edea', '#fed6e3'],
                    ['#ff9a9e', '#fecfef'],
                    ['#ffecd2', '#fcb69f']
                ],
                textColors: ['#ffffff', '#2c3e50'],
                accentColors: ['#f39c12', '#e74c3c', '#3498db', '#2ecc71']
            },
            minimal: {
                backgrounds: [
                    ['#ffffff', '#f8f9fa'],
                    ['#f8f9fa', '#e9ecef'],
                    ['#ffffff', '#ffffff'],
                    ['#fafafa', '#ffffff']
                ],
                textColors: ['#2c3e50', '#34495e', '#1a1a1a'],
                accentColors: ['#3498db', '#e74c3c', '#f39c12', '#2ecc71']
            },
            vibrant: {
                backgrounds: [
                    ['#ff6b6b', '#ee5a24'],
                    ['#0abde3', '#006ba6'],
                    ['#00d2d3', '#54a0ff'],
                    ['#5f27cd', '#341f97'],
                    ['#ff9ff3', '#f368e0'],
                    ['#feca57', '#ff9ff3']
                ],
                textColors: ['#ffffff', '#2c3e50'],
                accentColors: ['#ffffff', '#f1c40f', '#e67e22']
            },
            professional: {
                backgrounds: [
                    ['#2c3e50', '#34495e'],
                    ['#34495e', '#2c3e50'],
                    ['#1e3c72', '#2a5298'],
                    ['#0f3460', '#0f4c75']
                ],
                textColors: ['#ffffff', '#ecf0f1'],
                accentColors: ['#3498db', '#e74c3c', '#f39c12', '#2ecc71']
            }
        };
        
        this.icons = {
            副业: '💼',
            理财: '💰',
            技能: '🎯',
            创业: '🚀',
            省钱: '💡'
        };
    }

    async generateImage(content, style = 'gradient') {
        const styleConfig = this.styles[style];
        const bgColors = styleConfig.backgrounds[Math.floor(Math.random() * styleConfig.backgrounds.length)];
        const textColor = styleConfig.textColors[Math.floor(Math.random() * styleConfig.textColors.length)];
        const accentColor = styleConfig.accentColors[Math.floor(Math.random() * styleConfig.accentColors.length)];
        
        // 清空画布
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        // 绘制背景
        this.drawBackground(bgColors, style);
        
        // 绘制装饰元素
        this.drawDecorations(style, accentColor);
        
        // 绘制内容
        this.drawContent(content, textColor, accentColor, style);
        
        // 绘制边框和阴影效果
        this.drawBorder(style);
        
        return this.canvas.toDataURL('image/png');
    }

    drawBackground(colors, style) {
        if (style === 'minimal' && colors[0] === colors[1]) {
            // 纯色背景
            this.ctx.fillStyle = colors[0];
            this.ctx.fillRect(0, 0, this.width, this.height);
        } else {
            // 渐变背景
            const gradient = this.ctx.createLinearGradient(0, 0, this.width, this.height);
            gradient.addColorStop(0, colors[0]);
            gradient.addColorStop(1, colors[1]);
            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(0, 0, this.width, this.height);
        }
    }

    drawDecorations(style, accentColor) {
        this.ctx.save();
        
        switch (style) {
            case 'gradient':
                this.drawGradientDecorations(accentColor);
                break;
            case 'minimal':
                this.drawMinimalDecorations(accentColor);
                break;
            case 'vibrant':
                this.drawVibrantDecorations(accentColor);
                break;
            case 'professional':
                this.drawProfessionalDecorations(accentColor);
                break;
        }
        
        this.ctx.restore();
    }

    drawGradientDecorations(accentColor) {
        // 绘制圆形装饰
        this.ctx.globalAlpha = 0.1;
        this.ctx.fillStyle = accentColor;
        
        // 大圆
        this.ctx.beginPath();
        this.ctx.arc(this.width * 0.8, this.height * 0.2, 80, 0, Math.PI * 2);
        this.ctx.fill();
        
        // 小圆
        this.ctx.beginPath();
        this.ctx.arc(this.width * 0.2, this.height * 0.8, 50, 0, Math.PI * 2);
        this.ctx.fill();
        
        this.ctx.globalAlpha = 1;
    }

    drawMinimalDecorations(accentColor) {
        // 绘制简约线条
        this.ctx.strokeStyle = accentColor;
        this.ctx.lineWidth = 3;
        this.ctx.globalAlpha = 0.3;
        
        // 顶部线条
        this.ctx.beginPath();
        this.ctx.moveTo(30, 50);
        this.ctx.lineTo(this.width - 30, 50);
        this.ctx.stroke();
        
        // 底部线条
        this.ctx.beginPath();
        this.ctx.moveTo(30, this.height - 50);
        this.ctx.lineTo(this.width - 30, this.height - 50);
        this.ctx.stroke();
        
        this.ctx.globalAlpha = 1;
    }

    drawVibrantDecorations(accentColor) {
        // 绘制活力几何图形
        this.ctx.globalAlpha = 0.2;
        this.ctx.fillStyle = accentColor;
        
        // 三角形
        this.ctx.beginPath();
        this.ctx.moveTo(this.width * 0.9, this.height * 0.1);
        this.ctx.lineTo(this.width * 0.95, this.height * 0.2);
        this.ctx.lineTo(this.width * 0.85, this.height * 0.2);
        this.ctx.closePath();
        this.ctx.fill();
        
        // 矩形
        this.ctx.fillRect(20, this.height * 0.7, 30, 30);
        
        this.ctx.globalAlpha = 1;
    }

    drawProfessionalDecorations(accentColor) {
        // 绘制商务风格装饰
        this.ctx.strokeStyle = accentColor;
        this.ctx.lineWidth = 2;
        this.ctx.globalAlpha = 0.4;
        
        // 网格线
        for (let i = 0; i < 3; i++) {
            const x = this.width * (0.1 + i * 0.3);
            this.ctx.beginPath();
            this.ctx.moveTo(x, 30);
            this.ctx.lineTo(x, 70);
            this.ctx.stroke();
        }
        
        this.ctx.globalAlpha = 1;
    }

    drawContent(content, textColor, accentColor, style) {
        this.ctx.fillStyle = textColor;
        this.ctx.textAlign = 'center';
        
        // 绘制图标
        const icon = this.icons[content.type] || '💰';
        this.ctx.font = '48px Arial';
        this.ctx.fillText(icon, this.width / 2, 120);
        
        // 绘制标题
        this.drawTitle(content.title, textColor, accentColor, style);
        
        // 绘制关键信息
        this.drawKeyInfo(content, textColor, accentColor, style);
        
        // 绘制标签
        this.drawHashtags(content.hashtags, accentColor, style);
    }

    drawTitle(title, textColor, accentColor, style) {
        this.ctx.fillStyle = textColor;
        this.ctx.font = 'bold 24px Arial';
        this.ctx.textAlign = 'center';
        
        // 处理长标题换行
        const words = title.split('');
        const lines = [];
        let currentLine = '';
        
        for (let word of words) {
            const testLine = currentLine + word;
            const metrics = this.ctx.measureText(testLine);
            
            if (metrics.width > this.width - 60 && currentLine !== '') {
                lines.push(currentLine);
                currentLine = word;
            } else {
                currentLine = testLine;
            }
        }
        lines.push(currentLine);
        
        // 绘制标题行
        const startY = 180;
        lines.forEach((line, index) => {
            this.ctx.fillText(line, this.width / 2, startY + index * 35);
        });
    }

    drawKeyInfo(content, textColor, accentColor, style) {
        const keyPhrases = this.extractKeyPhrases(content);
        
        this.ctx.font = '18px Arial';
        this.ctx.fillStyle = accentColor;
        
        let y = 320;
        keyPhrases.forEach((phrase, index) => {
            if (index < 3) { // 最多显示3个关键信息
                this.ctx.fillText(phrase, this.width / 2, y);
                y += 30;
            }
        });
    }

    extractKeyPhrases(content) {
        const phrases = [];
        const text = content.content;
        
        // 提取收入相关信息
        const incomeMatch = text.match(/月入\d+[万千]?|收益\d+%|赚了\d+[万千]?/g);
        if (incomeMatch) phrases.push(incomeMatch[0]);
        
        // 提取时间相关信息
        const timeMatch = text.match(/\d+个月|\d+年|半年/g);
        if (timeMatch) phrases.push(`仅需${timeMatch[0]}`);
        
        // 提取特色信息
        if (text.includes('零基础')) phrases.push('零基础可学');
        if (text.includes('在家')) phrases.push('在家就能做');
        if (text.includes('时间自由')) phrases.push('时间自由');
        
        return phrases.slice(0, 3);
    }

    drawHashtags(hashtags, accentColor, style) {
        this.ctx.font = '14px Arial';
        this.ctx.fillStyle = accentColor;
        this.ctx.textAlign = 'center';
        
        const tags = hashtags.split(' ').slice(0, 4); // 最多显示4个标签
        const tagText = tags.join(' ');
        
        // 绘制标签背景
        const metrics = this.ctx.measureText(tagText);
        const bgWidth = metrics.width + 20;
        const bgHeight = 25;
        const bgX = (this.width - bgWidth) / 2;
        const bgY = this.height - 80;
        
        this.ctx.globalAlpha = 0.2;
        this.ctx.fillStyle = accentColor;
        this.ctx.fillRect(bgX, bgY, bgWidth, bgHeight);
        
        this.ctx.globalAlpha = 1;
        this.ctx.fillStyle = accentColor;
        this.ctx.fillText(tagText, this.width / 2, bgY + 18);
    }

    drawBorder(style) {
        if (style === 'minimal' || style === 'professional') {
            this.ctx.strokeStyle = 'rgba(0,0,0,0.1)';
            this.ctx.lineWidth = 1;
            this.ctx.strokeRect(0, 0, this.width, this.height);
        }
    }

    downloadImage(filename = 'xiaohongshu-content') {
        const link = document.createElement('a');
        link.download = `${filename}-${Date.now()}.png`;
        link.href = this.canvas.toDataURL();
        link.click();
    }
}
