class ContentGenerator {
    constructor() {
        this.templates = {
            副业: {
                titles: [
                    "🔥月入过万的{skill}副业，新手也能做！",
                    "💰{skill}副业实操分享，{time}赚了{amount}",
                    "✨零基础{skill}副业教程，{benefit}",
                    "🚀{skill}副业月入{amount}，分享全套方法",
                    "💡{skill}副业干货，{time}从0到{amount}",
                    "🎯{skill}副业攻略，{benefit}真的香！",
                    "⚡{skill}副业变现，{time}轻松月入{amount}",
                    "🌟{skill}副业经验分享，{benefit}必看"
                ],
                content: [
                    "姐妹们！今天分享一个超级适合新手的{skill}副业💰\n\n✅ 门槛低：{requirement}\n✅ 时间灵活：{timeFlexible}\n✅ 收入稳定：{income}\n\n📝 具体操作步骤：\n{steps}\n\n💡 新手建议：\n{tips}\n\n真的不要再说没有副业可做了，关键是要行动起来！{encouragement}",
                    
                    "分享一个我正在做的{skill}副业🔥\n\n💰 收入情况：{income}\n⏰ 时间投入：{timeInvestment}\n🎯 适合人群：{targetAudience}\n\n🚀 入门方法：\n{methods}\n\n⚠️ 注意事项：\n{warnings}\n\n{skill}副业真的很香，{benefit}，有想法的姐妹可以试试！",
                    
                    "{skill}副业实操干货来了！💪\n\n📊 我的{time}收益：{earnings}\n\n🔥 核心技巧：\n{coreSkills}\n\n📈 进阶方法：\n{advancedMethods}\n\n💎 避坑指南：\n{pitfalls}\n\n{conclusion}"
                ],
                variables: {
                    skill: ["写作", "设计", "翻译", "客服", "数据录入", "视频剪辑", "配音", "摄影", "直播", "电商"],
                    time: ["3个月", "半年", "1年", "2个月", "4个月"],
                    amount: ["5000+", "8000+", "过万", "1.5万", "2万+"],
                    benefit: ["时间自由", "在家就能做", "技能还能提升", "越做越轻松", "收入很稳定"],
                    requirement: ["只需要一台电脑", "会基础操作就行", "零基础可学", "门槛超低"],
                    timeFlexible: ["想做就做", "时间完全自由", "利用碎片时间", "不影响主业"],
                    income: ["月入3000-8000", "稳定月入5000+", "兼职月入过万", "收入持续增长"],
                    encouragement: ["机会总是留给有准备的人", "行动力比什么都重要", "坚持就是胜利"]
                }
            },
            理财: {
                titles: [
                    "💰{method}理财法，{time}收益{return}！",
                    "🔥{age}岁开始理财，{benefit}不是梦",
                    "✨{method}投资攻略，{result}真香",
                    "💡理财小白必看：{method}入门指南",
                    "🎯{method}理财经验，{time}赚了{amount}",
                    "⚡{method}理财干货，{benefit}必学",
                    "🌟理财规划分享，{method}让我{result}",
                    "📈{method}投资心得，{time}收益翻倍"
                ],
                content: [
                    "理财小白看过来！分享我的{method}理财经验💰\n\n📊 我的理财组合：\n{portfolio}\n\n💡 理财原则：\n{principles}\n\n📈 收益情况：{returns}\n\n⚠️ 风险提醒：\n{risks}\n\n理财真的要趁早，{advice}！",
                    
                    "{method}理财干货分享🔥\n\n✅ 适合人群：{audience}\n✅ 资金门槛：{threshold}\n✅ 预期收益：{expectedReturn}\n\n🎯 操作步骤：\n{steps}\n\n💎 进阶技巧：\n{advancedTips}\n\n{conclusion}",
                    
                    "分享我的{method}理财心得💪\n\n📝 理财目标：{goals}\n📊 资产配置：{allocation}\n📈 {time}收益：{performance}\n\n🔥 核心策略：\n{strategies}\n\n💡 经验总结：\n{lessons}\n\n{encouragement}"
                ],
                variables: {
                    method: ["基金定投", "股票投资", "债券配置", "银行理财", "指数基金", "混合基金"],
                    time: ["1年", "2年", "3年", "半年", "18个月"],
                    return: ["15%", "20%", "25%", "12%", "18%"],
                    age: ["25", "30", "35", "22", "28"],
                    benefit: ["财务自由", "被动收入", "资产增值", "稳定收益"],
                    result: ["收益翻倍", "资产增长", "实现目标", "超出预期"],
                    amount: ["10万", "15万", "20万", "8万", "12万"]
                }
            },
            技能: {
                titles: [
                    "🎯{skill}变现指南，{time}收入{amount}",
                    "💰{skill}接单经验，{benefit}真的香",
                    "🔥{skill}赚钱攻略，{result}超预期",
                    "✨{skill}变现干货，新手必看",
                    "🚀{skill}接单技巧，{time}月入过万",
                    "💡{skill}变现经验，{benefit}分享",
                    "⚡{skill}赚钱方法，{result}惊喜",
                    "🌟{skill}变现心得，{time}收获满满"
                ],
                content: [
                    "{skill}变现干货来了！💪\n\n💰 变现渠道：\n{channels}\n\n📊 收入情况：{income}\n\n🎯 接单技巧：\n{tips}\n\n📈 提升方法：\n{improvement}\n\n{skill}变现真的很香，{encouragement}！",
                    
                    "分享我的{skill}变现经验🔥\n\n✅ 技能要求：{requirements}\n✅ 市场需求：{demand}\n✅ 收费标准：{pricing}\n\n🚀 快速入门：\n{quickStart}\n\n💎 高级技巧：\n{advancedSkills}\n\n{conclusion}",
                    
                    "{skill}变现实操分享💰\n\n📝 我的{time}成长：{growth}\n\n🔥 核心能力：\n{coreAbilities}\n\n📈 收入增长：\n{incomeGrowth}\n\n💡 成功秘诀：\n{secrets}\n\n{advice}"
                ],
                variables: {
                    skill: ["PS设计", "视频剪辑", "文案写作", "翻译", "编程", "插画", "摄影", "配音"],
                    time: ["3个月", "半年", "1年", "2个月", "8个月"],
                    amount: ["5000+", "过万", "1.5万", "8000+", "2万+"],
                    benefit: ["时间自由", "技能提升", "收入稳定", "越做越轻松"],
                    result: ["收入翻倍", "订单不断", "客户满意", "技能进步"]
                }
            },
            创业: {
                titles: [
                    "🚀{project}创业分享，{time}营收{revenue}",
                    "💰{project}创业经验，{result}超预期",
                    "🔥{project}创业干货，{benefit}必看",
                    "✨{project}创业心得，{time}的成长",
                    "🎯{project}创业攻略，{result}分享",
                    "💡{project}创业故事，{benefit}满满",
                    "⚡{project}创业经历，{time}收获颇丰",
                    "🌟{project}创业感悟，{result}值得"
                ],
                content: [
                    "{project}创业分享来了！🚀\n\n💡 项目介绍：{introduction}\n\n📊 {time}数据：\n{data}\n\n🔥 成功关键：\n{successFactors}\n\n⚠️ 踩坑经验：\n{pitfalls}\n\n创业路上{encouragement}，{advice}！",
                    
                    "分享我的{project}创业经验💪\n\n✅ 启动资金：{funding}\n✅ 团队配置：{team}\n✅ 市场定位：{positioning}\n\n📈 成长历程：\n{journey}\n\n💎 核心策略：\n{strategies}\n\n{conclusion}",
                    
                    "{project}创业实战干货🔥\n\n📝 商业模式：{businessModel}\n📊 盈利情况：{profitability}\n📈 发展规划：{plans}\n\n💡 经验总结：\n{lessons}\n\n🎯 给新手的建议：\n{advice}\n\n{encouragement}"
                ],
                variables: {
                    project: ["电商店铺", "自媒体", "线上教育", "咨询服务", "小程序", "APP开发"],
                    time: ["半年", "1年", "2年", "8个月", "18个月"],
                    revenue: ["50万", "100万", "200万", "30万", "80万"],
                    result: ["盈利稳定", "快速增长", "用户认可", "团队壮大"],
                    benefit: ["经验丰富", "人脉拓展", "能力提升", "财富积累"]
                }
            },
            省钱: {
                titles: [
                    "💡{method}省钱法，{time}省了{amount}",
                    "🔥{category}省钱攻略，{benefit}必学",
                    "✨{method}省钱技巧，{result}惊喜",
                    "💰{category}省钱经验，{time}的收获",
                    "🎯{method}省钱干货，{benefit}分享",
                    "⚡{category}省钱心得，{result}满意",
                    "🌟{method}省钱指南，{time}见效果",
                    "💡{category}省钱妙招，{benefit}实用"
                ],
                content: [
                    "{method}省钱攻略分享！💰\n\n💡 省钱方法：\n{methods}\n\n📊 {time}成果：省了{savings}\n\n🔥 实用技巧：\n{tips}\n\n📱 推荐工具：\n{tools}\n\n省钱就是赚钱，{encouragement}！",
                    
                    "分享我的{category}省钱经验🔥\n\n✅ 省钱原则：{principles}\n✅ 具体方法：{specificMethods}\n✅ 注意事项：{precautions}\n\n📈 省钱效果：\n{results}\n\n💎 进阶技巧：\n{advancedTips}\n\n{conclusion}",
                    
                    "{category}省钱实操干货💪\n\n📝 我的省钱清单：\n{checklist}\n\n🎯 重点关注：\n{focus}\n\n📊 省钱统计：{statistics}\n\n💡 心得体会：\n{insights}\n\n{advice}"
                ],
                variables: {
                    method: ["记账", "比价", "优惠券", "团购", "二手", "DIY"],
                    category: ["生活用品", "服装", "美妆", "餐饮", "出行", "娱乐"],
                    time: ["1个月", "3个月", "半年", "1年", "2个月"],
                    amount: ["1000元", "2000元", "3000元", "5000元", "1500元"],
                    benefit: ["实用性强", "效果明显", "简单易学", "持续有效"],
                    result: ["省钱显著", "习惯养成", "生活质量不降", "意外收获"]
                }
            }
        };
        
        this.hashtags = {
            副业: ["#副业", "#赚钱", "#兼职", "#在家赚钱", "#副业推荐", "#月入过万", "#技能变现"],
            理财: ["#理财", "#投资", "#基金", "#股票", "#财务自由", "#被动收入", "#理财规划"],
            技能: ["#技能变现", "#接单", "#自由职业", "#在线赚钱", "#技能提升", "#副业技能"],
            创业: ["#创业", "#创业分享", "#商业模式", "#创业经验", "#创业故事", "#企业家"],
            省钱: ["#省钱", "#省钱攻略", "#理财", "#生活技巧", "#省钱妙招", "#节约"]
        };
    }

    generateContent(type, titleLength = 'medium', contentDetail = 'detailed') {
        const template = this.templates[type];
        if (!template) return null;

        // 生成标题
        const title = this.generateTitle(template, titleLength);
        
        // 生成内容
        const content = this.generateContentText(template, contentDetail);
        
        // 生成标签
        const hashtags = this.generateHashtags(type);

        return {
            title,
            content,
            hashtags,
            type,
            timestamp: new Date().toISOString()
        };
    }

    generateTitle(template, length) {
        const titles = template.titles;
        const baseTitle = titles[Math.floor(Math.random() * titles.length)];
        
        return this.fillTemplate(baseTitle, template.variables);
    }

    generateContentText(template, detail) {
        const contents = template.content;
        let baseContent = contents[Math.floor(Math.random() * contents.length)];
        
        // 根据详细度调整内容
        if (detail === 'brief') {
            baseContent = baseContent.split('\n').slice(0, 8).join('\n');
        } else if (detail === 'comprehensive') {
            baseContent += '\n\n💪 总结：坚持就是胜利，行动起来才能看到效果！\n\n❤️ 觉得有用的话记得点赞收藏哦~';
        }
        
        return this.fillTemplate(baseContent, template.variables);
    }

    generateHashtags(type) {
        const tags = this.hashtags[type];
        const selectedTags = [];
        
        // 随机选择5-8个标签
        const numTags = Math.floor(Math.random() * 4) + 5;
        const shuffled = [...tags].sort(() => 0.5 - Math.random());
        
        for (let i = 0; i < Math.min(numTags, shuffled.length); i++) {
            selectedTags.push(shuffled[i]);
        }
        
        return selectedTags.join(' ');
    }

    fillTemplate(template, variables) {
        let result = template;
        
        // 替换所有变量
        const matches = template.match(/\{(\w+)\}/g);
        if (matches) {
            matches.forEach(match => {
                const key = match.slice(1, -1);
                if (variables[key]) {
                    const value = variables[key][Math.floor(Math.random() * variables[key].length)];
                    result = result.replace(match, value);
                }
            });
        }
        
        // 生成具体的步骤、技巧等内容
        result = this.generateDetailedContent(result);
        
        return result;
    }

    generateDetailedContent(content) {
        const stepPatterns = {
            '{steps}': [
                '1️⃣ 注册相关平台账号\n2️⃣ 完善个人资料和作品集\n3️⃣ 主动寻找合适的项目\n4️⃣ 认真完成每一个订单',
                '1️⃣ 学习基础技能和工具使用\n2️⃣ 制作优质的作品展示\n3️⃣ 建立个人品牌和口碑\n4️⃣ 持续优化和提升服务',
                '1️⃣ 确定目标客户群体\n2️⃣ 制定合理的价格策略\n3️⃣ 提供优质的客户服务\n4️⃣ 建立长期合作关系'
            ],
            '{tips}': [
                '• 保持耐心，收入需要时间积累\n• 不断学习提升专业技能\n• 维护好客户关系很重要\n• 合理安排时间，避免过度疲劳',
                '• 诚信经营，口碑比什么都重要\n• 多平台发展，分散风险\n• 定期总结经验，持续改进\n• 保持积极心态，坚持就是胜利',
                '• 前期可能收入不高，要坚持\n• 多观察同行的成功经验\n• 建立自己的客户资源库\n• 不断优化工作流程和效率'
            ],
            '{methods}': [
                '• 通过专业平台接单\n• 社交媒体展示作品\n• 朋友圈推广服务\n• 加入相关社群交流',
                '• 建立个人网站或博客\n• 参与行业活动和比赛\n• 与其他从业者合作\n• 提供免费咨询建立信任',
                '• 优化个人简历和作品集\n• 主动联系潜在客户\n• 提供试用或样品服务\n• 建立推荐奖励机制'
            ]
        };

        Object.keys(stepPatterns).forEach(pattern => {
            if (content.includes(pattern)) {
                const options = stepPatterns[pattern];
                const selected = options[Math.floor(Math.random() * options.length)];
                content = content.replace(pattern, selected);
            }
        });

        return content;
    }
}
