<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小红书内容生成器 - 演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .demo-container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #4ecdc4;
        }
        .demo-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
        }
        .demo-content {
            line-height: 1.6;
            color: #555;
            white-space: pre-line;
        }
        .demo-hashtags {
            color: #4ecdc4;
            font-weight: 500;
            margin-top: 10px;
        }
        .generate-btn {
            display: block;
            width: 200px;
            margin: 20px auto;
            padding: 12px 24px;
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        .generate-btn:hover {
            transform: translateY(-2px);
        }
        .link-btn {
            display: block;
            width: 300px;
            margin: 30px auto;
            padding: 15px 30px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            text-decoration: none;
            text-align: center;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            transition: transform 0.3s ease;
        }
        .link-btn:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🔥 小红书赚钱内容生成器演示</h1>
        
        <div id="demoContent">
            <p style="text-align: center; color: #666; font-size: 1.1rem;">
                点击下方按钮查看生成效果，或直接进入完整版工具
            </p>
        </div>
        
        <button class="generate-btn" onclick="generateDemo()">🎯 生成演示内容</button>
        
        <a href="index.html" class="link-btn">🚀 进入完整版工具</a>
        
        <div style="margin-top: 40px; padding: 20px; background: #e8f5e8; border-radius: 8px;">
            <h3 style="color: #2d5a2d; margin-bottom: 15px;">✨ 功能亮点</h3>
            <ul style="color: #2d5a2d; line-height: 1.8;">
                <li><strong>5大赚钱主题</strong>：副业、理财、技能变现、创业、省钱</li>
                <li><strong>4种图片风格</strong>：渐变、简约、活力、商务</li>
                <li><strong>智能内容生成</strong>：自动填充关键数据和信息</li>
                <li><strong>一键下载图片</strong>：高清PNG格式，直接可用</li>
                <li><strong>历史记录管理</strong>：保存和重用生成的内容</li>
            </ul>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 8px;">
            <h3 style="color: #856404; margin-bottom: 15px;">💡 使用建议</h3>
            <ul style="color: #856404; line-height: 1.8;">
                <li>根据目标用户群体选择合适的内容类型和图片风格</li>
                <li>生成多个版本进行A/B测试，找到最佳效果</li>
                <li>结合个人真实经验对生成内容进行适当调整</li>
                <li>关注小红书最佳发布时间：工作日19-22点，周末10-12点和14-16点</li>
                <li>及时回复评论和私信，提高账号活跃度</li>
            </ul>
        </div>
    </div>

    <script src="contentGenerator.js"></script>
    <script>
        const generator = new ContentGenerator();
        const types = ['副业', '理财', '技能', '创业', '省钱'];
        
        function generateDemo() {
            const randomType = types[Math.floor(Math.random() * types.length)];
            const content = generator.generateContent(randomType, 'medium', 'detailed');
            
            if (content) {
                document.getElementById('demoContent').innerHTML = `
                    <div class="demo-section">
                        <div class="demo-title">${content.title}</div>
                        <div class="demo-content">${content.content}</div>
                        <div class="demo-hashtags">${content.hashtags}</div>
                    </div>
                `;
            }
        }
        
        // 页面加载时自动生成一个演示
        window.addEventListener('load', generateDemo);
    </script>
</body>
</html>
